import { renderHook, waitFor } from '@testing-library/react';
import { useMapData } from '../useMapData';

// Mock fetch
global.fetch = jest.fn();

describe('useMapData', () => {
    beforeEach(() => {
        fetch.mockClear();
    });

    test('should load and transform map data correctly', async () => {
        const mockData = {
            graph_info: { id: 'test_factory' },
            nodes: [
                { id: 'A1', x: 100, y: 200, type: 'storage' },
                { id: 'B1', x: 300, y: 400, type: 'workstation' }
            ],
            unidirectional_edges: [
                { id: 'edge1', start_node_id: 'A1', end_node_id: 'B1' }
            ],
            bidirectional_edges: [
                { id: 'path1', nodes: ['A1', 'B1'] }
            ],
            pallets: [
                { id: 'pallet1', location: 'A1' }
            ],
            vehicles: [
                { id: 'agv1', location: 'B1' }
            ]
        };

        fetch.mockResolvedValueOnce({
            ok: true,
            json: async () => mockData
        });

        const { result } = renderHook(() => useMapData('/test_map_data.json'));

        expect(result.current.isLoading).toBe(true);

        await waitFor(() => {
            expect(result.current.isLoading).toBe(false);
        });

        expect(result.current.mapData.nodes.size).toBe(2);
        expect(result.current.mapData.nodes.get('A1')).toEqual({
            id: 'A1',
            x: 100,
            y: 200,
            type: 'storage'
        });
        expect(result.current.mapData.unidirectional_edges).toHaveLength(1);
        expect(result.current.mapData.bidirectional_edges).toHaveLength(1);
        expect(result.current.mapData.pallets).toHaveLength(1);
        expect(result.current.mapData.vehicles).toHaveLength(1);
    });

    test('should handle legacy data format with Y-axis flip', async () => {
        const mockLegacyData = {
            graph_info: { id: 'legacy_factory' },
            nodes: [
                { id: 'A1', x: 100, y: 200 }
            ]
        };

        fetch.mockResolvedValueOnce({
            ok: true,
            json: async () => mockLegacyData
        });

        const { result } = renderHook(() => useMapData('/legacy_map_data.json'));

        await waitFor(() => {
            expect(result.current.isLoading).toBe(false);
        });

        // Y coordinate should be flipped for legacy data
        expect(result.current.mapData.nodes.get('A1').y).toBe(-200);
    });

    test('should handle fetch errors gracefully', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        
        fetch.mockRejectedValueOnce(new Error('Network error'));

        const { result } = renderHook(() => useMapData('/invalid_url.json'));

        await waitFor(() => {
            expect(result.current.isLoading).toBe(false);
        });

        expect(consoleSpy).toHaveBeenCalledWith(
            '解析地图JSON时出错:',
            expect.any(Error)
        );

        consoleSpy.mockRestore();
    });

    test('should handle HTTP errors', async () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        
        fetch.mockResolvedValueOnce({
            ok: false,
            statusText: 'Not Found'
        });

        const { result } = renderHook(() => useMapData('/not_found.json'));

        await waitFor(() => {
            expect(result.current.isLoading).toBe(false);
        });

        expect(consoleSpy).toHaveBeenCalled();
        consoleSpy.mockRestore();
    });

    test('should provide default values for missing properties', async () => {
        const mockMinimalData = {
            nodes: [
                { id: 'A1', x: 100, y: 200 }
            ]
        };

        fetch.mockResolvedValueOnce({
            ok: true,
            json: async () => mockMinimalData
        });

        const { result } = renderHook(() => useMapData('/minimal_data.json'));

        await waitFor(() => {
            expect(result.current.isLoading).toBe(false);
        });

        expect(result.current.mapData.unidirectional_edges).toEqual([]);
        expect(result.current.mapData.bidirectional_edges).toEqual([]);
        expect(result.current.mapData.pallets).toEqual([]);
        expect(result.current.mapData.vehicles).toEqual([]);
        expect(result.current.mapData.graph_info).toEqual({});
    });
});
