import React from 'react';

// Icon Components
const InfoIcon = React.memo(() => (
    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="16" x2="12" y2="12"></line>
        <line x1="12" y1="8" x2="12.01" y2="8"></line>
    </svg>
));

const LocationIcon = React.memo(() => (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
        <circle cx="12" cy="10" r="3"></circle>
    </svg>
));

const TagIcon = React.memo(() => (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
        <line x1="7" y1="7" x2="7.01" y2="7"></line>
    </svg>
));

const DeviceIcon = React.memo(() => (
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
        <line x1="8" y1="21" x2="16" y2="21"></line>
        <line x1="12" y1="17" x2="12" y2="21"></line>
    </svg>
));

// ====================================================================================
// --- FILE: src/components/DetailPanel.jsx ---
// 右侧详情面板组件，用于显示选中元素的详细信息。
// ====================================================================================
export const DetailPanel = ({ selectedElement, simulationData }) => {
    if (!selectedElement) {
        return (
            <div className="h-full w-80 bg-gray-900 text-white shadow-lg flex flex-col">
                <div className="p-4 border-b border-gray-700">
                    <h2 className="text-lg font-semibold flex items-center gap-2">
                        <InfoIcon />
                        详细信息
                    </h2>
                </div>
                <div className="flex-grow flex items-center justify-center">
                    <div className="text-center text-gray-400">
                        <InfoIcon className="mx-auto mb-3 w-12 h-12 opacity-50" />
                        <p className="text-sm">点击地图上的元素</p>
                        <p className="text-xs mt-1">查看详细信息</p>
                    </div>
                </div>
            </div>
        );
    }

    const renderElementDetails = () => {
        const { type, data } = selectedElement;
        
        return (
            <div className="space-y-4">
                {/* 基本信息 */}
                <div className="bg-gray-800 rounded-lg p-4">
                    <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                        <TagIcon />
                        基本信息
                    </h3>
                    <div className="space-y-2">
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-400">类型:</span>
                            <span className="text-sm text-white font-medium">
                                {type === 'node' ? '节点' :
                                 type === 'edge' ? '边' :
                                 type === 'car' ? 'AGV小车' :
                                 type === 'pallet' ? '托盘' :
                                 type || '未知'}
                            </span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-400">ID:</span>
                            <span className="text-sm text-white font-mono bg-gray-700 px-2 py-1 rounded">
                                {data?.id || 'N/A'}
                            </span>
                        </div>
                    </div>
                </div>

                {/* 位置信息 */}
                {(data?.x !== undefined && data?.y !== undefined) && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                            <LocationIcon />
                            位置信息
                        </h3>
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">X坐标:</span>
                                <span className="text-sm text-white font-mono">
                                    {data.x.toFixed(1)}
                                </span>
                            </div>
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">Y坐标:</span>
                                <span className="text-sm text-white font-mono">
                                    {data.y.toFixed(1)}
                                </span>
                            </div>
                        </div>
                    </div>
                )}

                {/* 设备信息 */}
                {data?.deviceType && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                            <DeviceIcon />
                            设备信息
                        </h3>
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">设备类型:</span>
                                <span className="text-sm text-white">
                                    {data.deviceType}
                                </span>
                            </div>
                        </div>
                    </div>
                )}

                {/* 连接信息 (对于节点) */}
                {type === 'node' && data?.connections && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3">连接节点</h3>
                        <div className="space-y-1 max-h-32 overflow-y-auto">
                            {data.connections.map((connection, index) => (
                                <div key={index} className="text-xs text-gray-400 bg-gray-700 px-2 py-1 rounded">
                                    {connection}
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* 边信息 (对于边) */}
                {type === 'edge' && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3">边信息</h3>
                        <div className="space-y-2">
                            {data?.from && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">起点:</span>
                                    <span className="text-sm text-white font-mono bg-gray-700 px-2 py-1 rounded">
                                        {data.from}
                                    </span>
                                </div>
                            )}
                            {data?.to && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">终点:</span>
                                    <span className="text-sm text-white font-mono bg-gray-700 px-2 py-1 rounded">
                                        {data.to}
                                    </span>
                                </div>
                            )}
                            {data?.weight !== undefined && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">权重:</span>
                                    <span className="text-sm text-white">
                                        {data.weight}
                                    </span>
                                </div>
                            )}
                            {data?.edgeType && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">类型:</span>
                                    <span className="text-sm text-white">
                                        {data.edgeType === 'bidirectional' ? '双向' : '单向'}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* AGV小车信息 */}
                {type === 'car' && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                            <DeviceIcon />
                            AGV信息
                        </h3>
                        <div className="space-y-2">
                            {data?.angle !== undefined && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">朝向角度:</span>
                                    <span className="text-sm text-white">
                                        {(data.angle * 180 / Math.PI).toFixed(1)}°
                                    </span>
                                </div>
                            )}
                            {data?.speed !== undefined && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">速度:</span>
                                    <span className="text-sm text-white">
                                        {data.speed} 单位/秒
                                    </span>
                                </div>
                            )}
                            {data?.status && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">状态:</span>
                                    <span className={`text-sm ${data.status === 'moving' ? 'text-green-400' : 'text-yellow-400'}`}>
                                        {data.status === 'moving' ? '移动中' : '停止'}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* 托盘信息 */}
                {type === 'pallet' && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2">
                            <TagIcon />
                            托盘信息
                        </h3>
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">形状:</span>
                                <span className="text-sm text-white">
                                    {data?.shape === 'large' ? '大型' :
                                     data?.shape === 'long' ? '长型' : '标准'}
                                </span>
                            </div>
                            {data?.weight !== undefined && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">重量:</span>
                                    <span className="text-sm text-white">
                                        {data.weight} kg
                                    </span>
                                </div>
                            )}
                            {data?.contents && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">内容:</span>
                                    <span className="text-sm text-white">
                                        {data.contents}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                )}

                {/* 模拟状态信息 */}
                {simulationData?.route && (
                    <div className="bg-gray-800 rounded-lg p-4">
                        <h3 className="text-sm font-semibold text-gray-300 mb-3">模拟状态</h3>
                        <div className="space-y-2">
                            <div className="flex justify-between items-center">
                                <span className="text-sm text-gray-400">路线状态:</span>
                                <span className="text-sm text-green-400">
                                    {simulationData.route.path ? '已规划' : '未规划'}
                                </span>
                            </div>
                            {simulationData.route.totalTime && (
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-400">总时长:</span>
                                    <span className="text-sm text-white">
                                        {Math.round(simulationData.route.totalTime)}s
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        );
    };

    return (
        <div className="h-full w-80 bg-gray-900 text-white shadow-lg flex flex-col">
            <div className="p-4 border-b border-gray-700">
                <h2 className="text-lg font-semibold flex items-center gap-2">
                    <InfoIcon />
                    详细信息
                </h2>
                <p className="text-xs text-gray-400 mt-1">
                    {selectedElement.type === 'node' ? '节点' : selectedElement.type === 'edge' ? '边' : '元素'} 详情
                </p>
            </div>
            <div className="flex-grow overflow-y-auto p-4">
                {renderElementDetails()}
            </div>
        </div>
    );
};
