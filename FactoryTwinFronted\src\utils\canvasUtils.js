// ====================================================================================
// --- FILE: src/utils/canvasUtils.js ---
// 此文件包含用于 Canvas 的所有原生绘图函数。
// ====================================================================================

export const hexToRgba = (hex, alpha) => {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

export const drawNode = (ctx, node, scale, isSelected) => {
    const radius = isSelected ? 8 / scale : 6 / scale;

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#FBBF24';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        ctx.beginPath();
        ctx.arc(node.x, node.y, radius + 4 / scale, 0, 2 * Math.PI);
        ctx.fillStyle = 'rgba(251, 191, 36, 0.3)';
        ctx.fill();
        ctx.restore();
    }

    ctx.beginPath();
    ctx.arc(node.x, node.y, radius, 0, 2 * Math.PI);
    ctx.fillStyle = isSelected ? '#FBBF24' : '#F59E0B';
    ctx.fill();
    ctx.strokeStyle = '#D97706';
    ctx.lineWidth = 1 / scale;
    ctx.stroke();
};

export const drawArrow = (ctx, fromX, fromY, toX, toY, headLength, color) => {
    const dx = toX - fromX;
    const dy = toY - fromY;
    const angle = Math.atan2(dy, dx);
    ctx.beginPath();
    ctx.moveTo(fromX, fromY);
    ctx.lineTo(toX, toY);
    ctx.strokeStyle = color;
    ctx.stroke();
    ctx.beginPath();
    ctx.moveTo(toX, toY);
    ctx.lineTo(toX - headLength * Math.cos(angle - Math.PI / 6), toY - headLength * Math.sin(angle - Math.PI / 6));
    ctx.lineTo(toX - headLength * Math.cos(angle + Math.PI / 6), toY - headLength * Math.sin(angle + Math.PI / 6));
    ctx.closePath();
    ctx.fillStyle = color;
    ctx.fill();
};

export const drawUnidirectionalEdge = (ctx, startNode, endNode, scale, isSelected) => {
    const color = isSelected ? '#60A5FA' : '#3B82F6';

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#60A5FA';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 8 / scale;
        ctx.strokeStyle = 'rgba(96, 165, 250, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected ? 4 : 2) / scale;
    drawArrow(ctx, startNode.x, startNode.y, endNode.x, endNode.y, 15 / scale, color);
};

export const drawBidirectionalEdge = (ctx, startNode, endNode, scale, isSelected) => {
    const color = isSelected ? '#A78BFA' : '#8B5CF6';

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#A78BFA';
        ctx.shadowBlur = 15 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        ctx.lineWidth = 10 / scale;
        ctx.strokeStyle = 'rgba(167, 139, 250, 0.4)';
        ctx.beginPath();
        ctx.moveTo(startNode.x, startNode.y);
        ctx.lineTo(endNode.x, endNode.y);
        ctx.stroke();
        ctx.restore();
    }

    ctx.lineWidth = (isSelected ? 6 : 4) / scale;
    ctx.strokeStyle = color;
    ctx.beginPath();
    ctx.moveTo(startNode.x, startNode.y);
    ctx.lineTo(endNode.x, endNode.y);
    ctx.stroke();
};

export const drawCar = (ctx, car, scale, isSelected) => {
    const { x, y, angle } = car.position;
    const carWidth = 12 / scale;
    const carLength = 20 / scale;

    ctx.save();
    ctx.translate(x, y);
    ctx.rotate(angle);

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#DC2626';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 外发光轮廓
        ctx.fillStyle = 'rgba(220, 38, 38, 0.3)';
        ctx.beginPath();
        ctx.moveTo(-carWidth / 2 - 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(carWidth / 2 + 4 / scale, carLength / 2 + 4 / scale);
        ctx.lineTo(carWidth * 0.8 + 4 / scale, -carLength / 2 - 4 / scale);
        ctx.lineTo(-carWidth * 0.8 - 4 / scale, -carLength / 2 - 4 / scale);
        ctx.closePath();
        ctx.fill();
        ctx.restore();
    }

    // 车身
    ctx.fillStyle = isSelected ? '#EF4444' : '#DC2626'; // Brighter red when selected
    ctx.beginPath();
    ctx.moveTo(-carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth / 2, carLength / 2);
    ctx.lineTo(carWidth * 0.8, -carLength / 2);
    ctx.lineTo(-carWidth * 0.8, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 挡风玻璃
    ctx.fillStyle = '#60A5FA'; // A light blue
    ctx.beginPath();
    ctx.moveTo(-carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.6, -carLength * 0.1);
    ctx.lineTo(carWidth * 0.7, -carLength / 2);
    ctx.lineTo(-carWidth * 0.7, -carLength / 2);
    ctx.closePath();
    ctx.fill();

    // 车头灯
    ctx.fillStyle = '#FBBF24'; // Yellow
    ctx.beginPath();
    ctx.arc(-carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.arc(carWidth * 0.5, -carLength/2, 2 / scale, 0, 2* Math.PI);
    ctx.fill();

    ctx.restore();
};

export const drawPallet = (ctx, pallet, scale, isSelected) => {
    const { x, y } = pallet.position;
    // 托盘的不同形状
    const shape = pallet.shape || 'standard';
    const width = (shape === 'large' ? 30 : 20) / scale;
    const height = (shape === 'long' ? 30 : 20) / scale;

    ctx.save();
    ctx.translate(x, y);

    // 绘制外发光效果（选中时）
    if (isSelected) {
        ctx.save();
        ctx.shadowColor = '#A16207';
        ctx.shadowBlur = 20 / scale;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // 外发光轮廓
        ctx.fillStyle = 'rgba(161, 98, 7, 0.3)';
        ctx.fillRect(-width / 2 - 4 / scale, -height / 2 - 4 / scale,
                    width + 8 / scale, height + 8 / scale);
        ctx.restore();
    }

    ctx.fillStyle = isSelected ? '#D97706' : '#A16207'; // Brighter brown when selected
    ctx.strokeStyle = '#422006';
    ctx.lineWidth = 1 / scale;
    ctx.fillRect(-width / 2, -height / 2, width, height);
    ctx.strokeRect(-width / 2, -height / 2, width, height);
    ctx.restore();
};