import { hexToRgba } from '../canvasUtils';

// <PERSON>ck <PERSON>vas Context for testing drawing functions
const createMockContext = () => ({
    beginPath: jest.fn(),
    arc: jest.fn(),
    fill: jest.fn(),
    stroke: jest.fn(),
    moveTo: jest.fn(),
    lineTo: jest.fn(),
    closePath: jest.fn(),
    save: jest.fn(),
    restore: jest.fn(),
    translate: jest.fn(),
    rotate: jest.fn(),
    fillRect: jest.fn(),
    strokeRect: jest.fn(),
    fillStyle: '',
    strokeStyle: '',
    lineWidth: 0
});

describe('canvasUtils', () => {
    describe('hexToRgba', () => {
        test('should convert hex to rgba correctly', () => {
            expect(hexToRgba('#FF0000', 0.5)).toBe('rgba(255, 0, 0, 0.5)');
            expect(hexToRgba('#00FF00', 1.0)).toBe('rgba(0, 255, 0, 1)');
            expect(hexToRgba('#0000FF', 0.25)).toBe('rgba(0, 0, 255, 0.25)');
        });

        test('should handle lowercase hex values', () => {
            expect(hexToRgba('#ff0000', 0.5)).toBe('rgba(255, 0, 0, 0.5)');
            expect(hexToRgba('#abc123', 0.8)).toBe('rgba(171, 193, 35, 0.8)');
        });
    });

    describe('drawing functions', () => {
        let mockCtx;

        beforeEach(() => {
            mockCtx = createMockContext();
        });

        test('drawNode should call correct canvas methods', () => {
            const { drawNode } = require('../canvasUtils');
            const node = { x: 100, y: 200 };
            const scale = 1;
            const isSelected = false;

            drawNode(mockCtx, node, scale, isSelected);

            expect(mockCtx.beginPath).toHaveBeenCalled();
            expect(mockCtx.arc).toHaveBeenCalledWith(100, 200, 6, 0, 2 * Math.PI);
            expect(mockCtx.fill).toHaveBeenCalled();
            expect(mockCtx.stroke).toHaveBeenCalled();
        });

        test('drawNode should use different radius when selected', () => {
            const { drawNode } = require('../canvasUtils');
            const node = { x: 100, y: 200 };
            const scale = 1;
            const isSelected = true;

            drawNode(mockCtx, node, scale, isSelected);

            expect(mockCtx.arc).toHaveBeenCalledWith(100, 200, 8, 0, 2 * Math.PI);
        });

        test('drawPallet should handle different shapes', () => {
            const { drawPallet } = require('../canvasUtils');
            
            // Test standard pallet
            const standardPallet = {
                position: { x: 100, y: 200 },
                shape: 'standard'
            };
            
            drawPallet(mockCtx, standardPallet, 1);
            
            expect(mockCtx.save).toHaveBeenCalled();
            expect(mockCtx.translate).toHaveBeenCalledWith(100, 200);
            expect(mockCtx.fillRect).toHaveBeenCalledWith(-10, -10, 20, 20);
            expect(mockCtx.restore).toHaveBeenCalled();

            // Reset mock
            mockCtx = createMockContext();

            // Test large pallet
            const largePallet = {
                position: { x: 100, y: 200 },
                shape: 'large'
            };
            
            drawPallet(mockCtx, largePallet, 1);
            expect(mockCtx.fillRect).toHaveBeenCalledWith(-15, -10, 30, 20);

            // Reset mock
            mockCtx = createMockContext();

            // Test long pallet
            const longPallet = {
                position: { x: 100, y: 200 },
                shape: 'long'
            };
            
            drawPallet(mockCtx, longPallet, 1);
            expect(mockCtx.fillRect).toHaveBeenCalledWith(-10, -15, 20, 30);
        });

        test('drawCar should handle rotation and positioning', () => {
            const { drawCar } = require('../canvasUtils');
            const car = {
                position: { x: 100, y: 200, angle: Math.PI / 4 }
            };

            drawCar(mockCtx, car, 1);

            expect(mockCtx.save).toHaveBeenCalled();
            expect(mockCtx.translate).toHaveBeenCalledWith(100, 200);
            expect(mockCtx.rotate).toHaveBeenCalledWith(Math.PI / 4);
            expect(mockCtx.restore).toHaveBeenCalled();
        });
    });
});
